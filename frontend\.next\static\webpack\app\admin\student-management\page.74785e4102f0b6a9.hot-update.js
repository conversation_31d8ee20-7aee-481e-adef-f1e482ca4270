"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/student-management/page",{

/***/ "(app-pages-browser)/./src/app/admin/student-management/page.jsx":
/*!***************************************************!*\
  !*** ./src/app/admin/student-management/page.jsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ StudentManagement)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/refresh-cw.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowLeft,Calendar,GraduationCap,RefreshCw,Save,Search,User,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_students__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../../api/students */ \"(app-pages-browser)/./src/api/students.js\");\n/* harmony import */ var _api_auth__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../api/auth */ \"(app-pages-browser)/./src/api/auth.js\");\n/* harmony import */ var _StudentDropdown__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./StudentDropdown */ \"(app-pages-browser)/./src/app/admin/student-management/StudentDropdown.jsx\");\n/* harmony import */ var _StudentProfile__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./StudentProfile */ \"(app-pages-browser)/./src/app/admin/student-management/StudentProfile.jsx\");\n/* harmony import */ var _DepartmentCards__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./DepartmentCards */ \"(app-pages-browser)/./src/app/admin/student-management/DepartmentCards.jsx\");\n/* harmony import */ var _PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./PassoutYearCards */ \"(app-pages-browser)/./src/app/admin/student-management/PassoutYearCards.jsx\");\n/* harmony import */ var _StudentList__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./StudentList */ \"(app-pages-browser)/./src/app/admin/student-management/StudentList.jsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction StudentManagement() {\n    var _departmentOptions_find, _departmentOptions_find1;\n    _s();\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [debouncedSearchTerm, setDebouncedSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [selectedDepartment, setSelectedDepartment] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [selectedYear, setSelectedYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('all');\n    const [selectedStudent, setSelectedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [editedStudent, setEditedStudent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const dropdownRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isYearDropdownOpen, setIsYearDropdownOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [students, setStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [allStudents, setAllStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isRetrying, setIsRetrying] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [currentPage, setCurrentPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalPages, setTotalPages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(1);\n    const [totalStudents, setTotalStudents] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [availableYears, setAvailableYears] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [departmentStats, setDepartmentStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedPassoutYear, setSelectedPassoutYear] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cgpaMin, setCgpaMin] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [cgpaMax, setCgpaMax] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    // Dropdown options\n    const departmentOptions = [\n        {\n            value: 'Computer Science',\n            label: 'Computer Science'\n        },\n        {\n            value: 'Electronics',\n            label: 'Electronics'\n        },\n        {\n            value: 'Mechanical',\n            label: 'Mechanical'\n        },\n        {\n            value: 'Civil',\n            label: 'Civil'\n        },\n        {\n            value: 'Electrical',\n            label: 'Electrical'\n        },\n        {\n            value: 'Information Technology',\n            label: 'Information Technology'\n        },\n        {\n            value: 'Chemical',\n            label: 'Chemical'\n        },\n        {\n            value: 'Biotechnology',\n            label: 'Biotechnology'\n        }\n    ];\n    // Fetch all students for complete dataset\n    const fetchAllStudents = async ()=>{\n        try {\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            let allData = [];\n            let page = 1;\n            let hasMore = true;\n            while(hasMore){\n                const response = await _api_students__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.getStudents({\n                    page,\n                    page_size: 100\n                });\n                allData = [\n                    ...allData,\n                    ...response.data\n                ];\n                if (response.pagination) {\n                    hasMore = page < response.pagination.total_pages;\n                    page++;\n                } else {\n                    hasMore = false;\n                }\n            }\n            const studentsData = allData.map((student)=>({\n                    id: student.id,\n                    rollNumber: student.student_id || 'N/A',\n                    name: \"\".concat(student.first_name || '', \" \").concat(student.last_name || '').trim() || 'Unknown',\n                    email: student.contact_email || student.email || 'N/A',\n                    phone: student.phone || 'N/A',\n                    department: student.branch || 'N/A',\n                    year: getYearFromBranch(student.branch, student),\n                    cgpa: student.gpa || 'N/A',\n                    gpa: student.gpa || 'N/A',\n                    address: student.address || 'N/A',\n                    dateOfBirth: student.date_of_birth || '',\n                    parentContact: student.parent_contact || 'N/A',\n                    education: student.education || 'N/A',\n                    skills: student.skills || [],\n                    // Academic details\n                    joining_year: student.joining_year || student.admission_year || '',\n                    passout_year: student.passout_year || student.graduation_year || '',\n                    // Class XII details\n                    twelfth_cgpa: student.twelfth_cgpa || student.class_12_cgpa || '',\n                    twelfth_percentage: student.twelfth_percentage || student.class_12_percentage || '',\n                    twelfth_year_of_passing: student.twelfth_year_of_passing || student.class_12_year || '',\n                    twelfth_school: student.twelfth_school || student.class_12_school || '',\n                    twelfth_board: student.twelfth_board || student.class_12_board || '',\n                    twelfth_location: student.twelfth_location || student.class_12_location || '',\n                    twelfth_specialization: student.twelfth_specialization || student.class_12_stream || '',\n                    // Class X details\n                    tenth_cgpa: student.tenth_cgpa || student.class_10_cgpa || '',\n                    tenth_percentage: student.tenth_percentage || student.class_10_percentage || '',\n                    tenth_year_of_passing: student.tenth_year_of_passing || student.class_10_year || '',\n                    tenth_school: student.tenth_school || student.class_10_school || '',\n                    tenth_board: student.tenth_board || student.class_10_board || '',\n                    tenth_location: student.tenth_location || student.class_10_location || '',\n                    tenth_specialization: student.tenth_specialization || student.class_10_stream || '',\n                    // Address details\n                    city: student.city || '',\n                    district: student.district || '',\n                    state: student.state || '',\n                    pincode: student.pincode || student.pin_code || '',\n                    country: student.country || 'India',\n                    // Certificate URLs\n                    tenth_certificate: student.tenth_certificate || student.class_10_certificate || '',\n                    twelfth_certificate: student.twelfth_certificate || student.class_12_certificate || '',\n                    tenth_certificate_url: student.tenth_certificate_url || student.class_10_certificate_url || '',\n                    twelfth_certificate_url: student.twelfth_certificate_url || student.class_12_certificate_url || '',\n                    // Resume details\n                    resume: student.resume || '',\n                    resume_url: student.resume_url || '',\n                    // Semester-wise CGPA data - use actual backend data\n                    semester_cgpas: student.semester_marksheets || [],\n                    semester_marksheets: student.semester_marksheets || []\n                }));\n            setAllStudents(studentsData);\n            setAvailableYears(getAvailableYears(studentsData));\n            setDepartmentStats(getDepartmentStats(studentsData));\n            return studentsData;\n        } catch (err) {\n            console.error('Error fetching all students:', err);\n            throw err;\n        }\n    };\n    // Debounce search term\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const timer = setTimeout({\n                \"StudentManagement.useEffect.timer\": ()=>{\n                    setDebouncedSearchTerm(searchTerm);\n                }\n            }[\"StudentManagement.useEffect.timer\"], 300); // 300ms delay\n            return ({\n                \"StudentManagement.useEffect\": ()=>clearTimeout(timer)\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], [\n        searchTerm\n    ]);\n    // Fetch students from Django backend with pagination\n    const fetchStudents = async function() {\n        let page = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 1;\n        try {\n            setLoading(true);\n            setError(null);\n            setIsRetrying(false);\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                throw new Error('No authentication token found. Please login first.');\n            }\n            // Use allStudents if already loaded, otherwise fetch all\n            let allData = allStudents;\n            if (allStudents.length === 0) {\n                allData = await fetchAllStudents();\n            }\n            // Apply filters to get the filtered dataset\n            let filteredData = allData;\n            if (selectedDepartment) {\n                filteredData = filteredData.filter((student)=>student.department === selectedDepartment);\n            }\n            if (selectedYear !== 'all') {\n                filteredData = filteredData.filter((student)=>student.year === selectedYear);\n            }\n            if (debouncedSearchTerm) {\n                const searchLower = debouncedSearchTerm.toLowerCase();\n                filteredData = filteredData.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n            }\n            // Implement client-side pagination\n            const studentsPerPage = 10;\n            const startIndex = (page - 1) * studentsPerPage;\n            const endIndex = startIndex + studentsPerPage;\n            const paginatedStudents = filteredData.slice(startIndex, endIndex);\n            setStudents(paginatedStudents);\n            setCurrentPage(page);\n            setTotalPages(Math.ceil(filteredData.length / studentsPerPage));\n            setTotalStudents(filteredData.length);\n            setLoading(false);\n        } catch (err) {\n            var _err_response, _err_response1;\n            console.error('Error fetching students:', err);\n            if (((_err_response = err.response) === null || _err_response === void 0 ? void 0 : _err_response.status) === 401) {\n                setError('Authentication failed. Please login again.');\n            } else if (((_err_response1 = err.response) === null || _err_response1 === void 0 ? void 0 : _err_response1.status) === 403) {\n                setError('You do not have permission to view students. Admin access required.');\n            } else if (err.message.includes('token')) {\n                setError('Please login to access student management.');\n            } else {\n                setError(\"Error: \".concat(err.message));\n            }\n            setStudents([]);\n            setLoading(false);\n        }\n    };\n    // Helper function to determine year from branch (you can customize this logic)\n    const getYearFromBranch = (branch, student)=>{\n        if (student && student.joining_year && student.passout_year) {\n            return \"\".concat(student.joining_year, \"-\").concat(student.passout_year);\n        }\n        return 'N/A';\n    };\n    // Initial data fetch\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Add this useEffect after your existing useEffect\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            // Check if user is authenticated\n            const token = (0,_api_auth__WEBPACK_IMPORTED_MODULE_3__.getAuthToken)();\n            if (!token) {\n                // Redirect to login page or show login prompt\n                setError('Please login to access student management.');\n                setLoading(false);\n                return;\n            }\n            fetchStudents();\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Helper function to extract year from student ID (assuming format like CS2021001)\n    const getYearFromStudentId = (studentId)=>{\n        if (studentId && studentId.length >= 6) {\n            const yearPart = studentId.substring(2, 6);\n            if (!isNaN(yearPart)) {\n                return \"\".concat(4 - (new Date().getFullYear() - parseInt(yearPart)), \"th Year\");\n            }\n        }\n        return 'Unknown';\n    };\n    // Close dropdown when clicking outside\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            const handleClickOutside = {\n                \"StudentManagement.useEffect.handleClickOutside\": (event)=>{\n                    if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {\n                        setIsYearDropdownOpen(false);\n                    }\n                }\n            }[\"StudentManagement.useEffect.handleClickOutside\"];\n            document.addEventListener('mousedown', handleClickOutside);\n            return ({\n                \"StudentManagement.useEffect\": ()=>{\n                    document.removeEventListener('mousedown', handleClickOutside);\n                }\n            })[\"StudentManagement.useEffect\"];\n        }\n    }[\"StudentManagement.useEffect\"], []);\n    // Get available years from students data\n    const getAvailableYears = (studentsData)=>{\n        const years = [\n            ...new Set(studentsData.map((student)=>student.year).filter((year)=>year && year !== 'N/A'))\n        ];\n        return years.sort();\n    };\n    // Get department statistics\n    const getDepartmentStats = (studentsData)=>{\n        const stats = {};\n        studentsData.forEach((student)=>{\n            if (student.department && student.department !== 'N/A') {\n                stats[student.department] = (stats[student.department] || 0) + 1;\n            }\n        });\n        return Object.entries(stats).map((param)=>{\n            let [department, count] = param;\n            return {\n                department,\n                count\n            };\n        });\n    };\n    // Get available passout years for selected department\n    const getAvailablePassoutYears = ()=>{\n        if (!selectedDepartment) return [];\n        const years = allStudents.filter((s)=>s.department === selectedDepartment && s.year && s.year !== 'N/A').map((s)=>{\n            // Extract passout year from year string (format: \"joining-passout\")\n            const parts = s.year.split('-');\n            return parts.length === 2 ? parts[1] : null;\n        }).filter((y)=>y).map(Number).filter((y)=>!isNaN(y));\n        // Unique and descending\n        return Array.from(new Set(years)).sort((a, b)=>b - a);\n    };\n    // Filter students for selected department and passout year\n    const getFilteredStudents = ()=>{\n        let filtered = allStudents;\n        if (selectedDepartment) {\n            filtered = filtered.filter((s)=>s.department === selectedDepartment);\n        }\n        if (selectedPassoutYear) {\n            filtered = filtered.filter((s)=>{\n                if (!s.year || s.year === 'N/A') return false;\n                const parts = s.year.split('-');\n                return parts.length === 2 && parts[1] === String(selectedPassoutYear);\n            });\n        }\n        if (debouncedSearchTerm) {\n            const searchLower = debouncedSearchTerm.toLowerCase();\n            filtered = filtered.filter((student)=>student.name.toLowerCase().includes(searchLower) || student.rollNumber.toLowerCase().includes(searchLower));\n        }\n        // CGPA filter\n        filtered = filtered.filter((student)=>{\n            const cgpa = parseFloat(student.cgpa);\n            if (cgpaMin && (isNaN(cgpa) || cgpa < parseFloat(cgpaMin))) return false;\n            if (cgpaMax && (isNaN(cgpa) || cgpa > parseFloat(cgpaMax))) return false;\n            return true;\n        });\n        return filtered;\n    };\n    // Update filters and refetch when dependencies change (but not searchTerm)\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"StudentManagement.useEffect\": ()=>{\n            if (allStudents.length > 0) {\n                fetchStudents(1); // Reset to page 1 when filters change\n            }\n        }\n    }[\"StudentManagement.useEffect\"], [\n        selectedDepartment,\n        selectedYear,\n        debouncedSearchTerm,\n        selectedPassoutYear\n    ]);\n    // Filter students based on selected department, year, and search term\n    const filteredStudents = students; // Students are already filtered in fetchStudents\n    const handleStudentClick = (student)=>{\n        setSelectedStudent(student);\n        setEditedStudent({\n            ...student\n        });\n        setIsEditing(false);\n    };\n    const handleBackToList = ()=>{\n        setSelectedStudent(null);\n        setIsEditing(false);\n        setEditedStudent(null);\n    };\n    const handleBackToDepartments = ()=>{\n        setSelectedDepartment(null);\n        setSelectedYear('all');\n        setSearchTerm('');\n    };\n    const handleEdit = ()=>{\n        setIsEditing(true);\n    };\n    const handleSave = async ()=>{\n        try {\n            var _editedStudent_name, _editedStudent_name1;\n            setLoading(true);\n            // Helper function to clean data\n            const cleanValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                return value;\n            };\n            // Helper function to clean numeric values\n            const cleanNumericValue = (value)=>{\n                if (value === '' || value === null || value === undefined) {\n                    return null;\n                }\n                const parsed = parseInt(value);\n                return isNaN(parsed) ? null : parsed;\n            };\n            // Prepare the data for backend update\n            const updateData = {\n                // Basic information\n                first_name: cleanValue((_editedStudent_name = editedStudent.name) === null || _editedStudent_name === void 0 ? void 0 : _editedStudent_name.split(' ')[0]) || '',\n                last_name: cleanValue((_editedStudent_name1 = editedStudent.name) === null || _editedStudent_name1 === void 0 ? void 0 : _editedStudent_name1.split(' ').slice(1).join(' ')) || '',\n                student_id: cleanValue(editedStudent.rollNumber),\n                contact_email: cleanValue(editedStudent.email),\n                phone: cleanValue(editedStudent.phone),\n                branch: cleanValue(editedStudent.department),\n                gpa: cleanValue(editedStudent.gpa),\n                // Academic details\n                joining_year: cleanNumericValue(editedStudent.joining_year),\n                passout_year: cleanNumericValue(editedStudent.passout_year),\n                // Personal details\n                date_of_birth: cleanValue(editedStudent.dateOfBirth),\n                address: cleanValue(editedStudent.address),\n                city: cleanValue(editedStudent.city),\n                district: cleanValue(editedStudent.district),\n                state: cleanValue(editedStudent.state),\n                pincode: cleanValue(editedStudent.pincode),\n                country: cleanValue(editedStudent.country),\n                parent_contact: cleanValue(editedStudent.parentContact),\n                education: cleanValue(editedStudent.education),\n                skills: Array.isArray(editedStudent.skills) ? editedStudent.skills.join(', ') : cleanValue(editedStudent.skills),\n                // Academic scores\n                tenth_cgpa: cleanValue(editedStudent.tenth_cgpa),\n                tenth_percentage: cleanValue(editedStudent.tenth_percentage),\n                tenth_board: cleanValue(editedStudent.tenth_board),\n                tenth_school: cleanValue(editedStudent.tenth_school),\n                tenth_year_of_passing: cleanValue(editedStudent.tenth_year_of_passing),\n                tenth_location: cleanValue(editedStudent.tenth_location),\n                tenth_specialization: cleanValue(editedStudent.tenth_specialization),\n                twelfth_cgpa: cleanValue(editedStudent.twelfth_cgpa),\n                twelfth_percentage: cleanValue(editedStudent.twelfth_percentage),\n                twelfth_board: cleanValue(editedStudent.twelfth_board),\n                twelfth_school: cleanValue(editedStudent.twelfth_school),\n                twelfth_year_of_passing: cleanValue(editedStudent.twelfth_year_of_passing),\n                twelfth_location: cleanValue(editedStudent.twelfth_location),\n                twelfth_specialization: cleanValue(editedStudent.twelfth_specialization)\n            };\n            // Add semester CGPAs if they exist\n            if (editedStudent.semester_cgpas && Array.isArray(editedStudent.semester_cgpas)) {\n                editedStudent.semester_cgpas.forEach((semesterData)=>{\n                    if (semesterData.semester >= 1 && semesterData.semester <= 8) {\n                        updateData[\"semester\".concat(semesterData.semester, \"_cgpa\")] = cleanValue(semesterData.cgpa);\n                    }\n                });\n            }\n            // Remove null values to avoid sending unnecessary data\n            const cleanedUpdateData = Object.fromEntries(Object.entries(updateData).filter((param)=>{\n                let [key, value] = param;\n                return value !== null;\n            }));\n            // Debug logging\n            console.log('Update data being sent:', cleanedUpdateData);\n            console.log('Student ID:', editedStudent.id);\n            // Make API call to update student\n            const updatedStudent = await _api_students__WEBPACK_IMPORTED_MODULE_2__.studentsAPI.updateStudent(editedStudent.id, cleanedUpdateData);\n            // Update the student in the list with the response data\n            const updatedStudentData = {\n                ...editedStudent,\n                ...updatedStudent,\n                name: \"\".concat(updatedStudent.first_name || '', \" \").concat(updatedStudent.last_name || '').trim(),\n                rollNumber: updatedStudent.student_id,\n                email: updatedStudent.contact_email,\n                department: updatedStudent.branch,\n                gpa: updatedStudent.gpa\n            };\n            setStudents((prev)=>prev.map((student)=>student.id === editedStudent.id ? updatedStudentData : student));\n            setSelectedStudent(updatedStudentData);\n            setIsEditing(false);\n            // Show success message\n            alert('Student profile updated successfully!');\n        } catch (error) {\n            var _error_response_data, _error_response, _error_response_data1, _error_response1;\n            console.error('Error updating student:', error);\n            const errorMessage = ((_error_response = error.response) === null || _error_response === void 0 ? void 0 : (_error_response_data = _error_response.data) === null || _error_response_data === void 0 ? void 0 : _error_response_data.detail) || ((_error_response1 = error.response) === null || _error_response1 === void 0 ? void 0 : (_error_response_data1 = _error_response1.data) === null || _error_response_data1 === void 0 ? void 0 : _error_response_data1.message) || error.message || 'Failed to update student profile. Please try again.';\n            alert(\"Update failed: \".concat(errorMessage));\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleCancel = ()=>{\n        setEditedStudent({\n            ...selectedStudent\n        });\n        setIsEditing(false);\n    };\n    const handleInputChange = (field, value)=>{\n        setEditedStudent((prev)=>({\n                ...prev,\n                [field]: value\n            }));\n    };\n    // Handle retry button click\n    const handleRetry = ()=>{\n        setIsRetrying(true);\n        fetchStudents();\n    };\n    // Help developers find the correct API endpoint\n    const debugBackend = ()=>{\n        window.open('http://localhost:8000/admin/');\n    };\n    const handleSearch = ()=>{\n        // Force immediate search without waiting for debounce\n        setDebouncedSearchTerm(searchTerm);\n        setCurrentPage(1);\n    };\n    // Handle pagination\n    const handlePageChange = (newPage)=>{\n        if (newPage >= 1 && newPage <= totalPages) {\n            fetchStudents(newPage);\n        }\n    };\n    // Handle search input change\n    const handleSearchInputChange = (e)=>{\n        setSearchTerm(e.target.value);\n    // Don't trigger immediate search, let debounce handle it\n    };\n    // Handle search input key press\n    const handleSearchKeyDown = (e)=>{\n        if (e.key === 'Enter') {\n            e.preventDefault();\n            handleSearch();\n        }\n    };\n    if (loading) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500 mb-4\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 556,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-gray-600\",\n                    children: \"Loading students...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 557,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 555,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 554,\n        columnNumber: 5\n    }, this);\n    if (error && students.length === 0) return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center h-full\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-500 mb-4 text-center max-w-md\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"font-semibold text-lg mb-2\",\n                            children: \"Access Error\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 566,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: error\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 567,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-4 text-sm text-gray-600\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    children: \"Possible solutions:\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 569,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                    className: \"list-disc list-inside mt-2 text-left\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Make sure you're logged in with admin credentials\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 571,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Check if your session has expired\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 572,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Verify Django server is running on port 8000\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 573,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                            children: \"Ensure proper permissions are set in Django\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                            lineNumber: 574,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 570,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 568,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 565,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-3 mt-4\",\n                    children: [\n                        !error.includes('login') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: handleRetry,\n                            className: \"flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\",\n                            disabled: isRetrying,\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"w-4 h-4 \".concat(isRetrying ? 'animate-spin' : '')\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 585,\n                                    columnNumber: 15\n                                }, this),\n                                isRetrying ? 'Retrying...' : 'Retry'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 580,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>window.location.href = '/login',\n                            className: \"flex items-center gap-2 px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowLeft_Calendar_GraduationCap_RefreshCw_Save_Search_User_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                    className: \"w-4 h-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                                    lineNumber: 593,\n                                    columnNumber: 13\n                                }, this),\n                                \"Go to Login\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                            lineNumber: 589,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                    lineNumber: 578,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 564,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 563,\n        columnNumber: 5\n    }, this);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex-1 p-6 ml-20 overflow-y-auto h-full\",\n        children: !selectedStudent ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n            children: !selectedDepartment ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_DepartmentCards__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                departmentOptions: departmentOptions,\n                departmentStats: departmentStats,\n                allStudents: allStudents,\n                onSelect: setSelectedDepartment\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 606,\n                columnNumber: 13\n            }, this) : !selectedPassoutYear ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_PassoutYearCards__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                departmentLabel: (_departmentOptions_find = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find === void 0 ? void 0 : _departmentOptions_find.label,\n                onBack: handleBackToDepartments,\n                getAvailablePassoutYears: getAvailablePassoutYears,\n                allStudents: allStudents,\n                selectedDepartment: selectedDepartment,\n                onSelectYear: setSelectedPassoutYear\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 613,\n                columnNumber: 13\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentList__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                departmentLabel: (_departmentOptions_find1 = departmentOptions.find((d)=>d.value === selectedDepartment)) === null || _departmentOptions_find1 === void 0 ? void 0 : _departmentOptions_find1.label,\n                passoutYear: selectedPassoutYear,\n                onBack: ()=>setSelectedPassoutYear(null),\n                searchTerm: searchTerm,\n                handleSearchInputChange: handleSearchInputChange,\n                handleSearchKeyDown: handleSearchKeyDown,\n                cgpaMin: cgpaMin,\n                setCgpaMin: setCgpaMin,\n                cgpaMax: cgpaMax,\n                setCgpaMax: setCgpaMax,\n                handleSearch: handleSearch,\n                getFilteredStudents: getFilteredStudents,\n                currentPage: currentPage,\n                handlePageChange: handlePageChange,\n                handleStudentClick: handleStudentClick,\n                loading: loading\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n                lineNumber: 622,\n                columnNumber: 13\n            }, this)\n        }, void 0, false) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_StudentProfile__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n            selectedStudent: selectedStudent,\n            editedStudent: editedStudent,\n            isEditing: isEditing,\n            handleBackToList: handleBackToList,\n            handleEdit: handleEdit,\n            handleSave: handleSave,\n            handleCancel: handleCancel,\n            handleInputChange: handleInputChange,\n            departmentOptions: departmentOptions\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n            lineNumber: 643,\n            columnNumber: 9\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\VS CODE\\\\combine\\\\frontend\\\\src\\\\app\\\\admin\\\\student-management\\\\page.jsx\",\n        lineNumber: 602,\n        columnNumber: 5\n    }, this);\n}\n_s(StudentManagement, \"vV3ywjzqfJaaIG37j/kyv5UkL2w=\");\n_c = StudentManagement;\nvar _c;\n$RefreshReg$(_c, \"StudentManagement\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/admin/student-management/page.jsx\n"));

/***/ })

});