from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import StudentProfile
from jobs.models import JobPosting, JobApplication
from django.utils import timezone

User = get_user_model()


class UserSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('id', 'email', 'first_name', 'last_name', 'user_type')


class SemesterMarksheetSerializer(serializers.Serializer):
    semester = serializers.IntegerField()
    cgpa = serializers.CharField()
    marksheet_file = serializers.FileField(required=False)
    marksheet_url = serializers.SerializerMethodField()
    upload_date = serializers.DateTimeField()
    
    def get_marksheet_url(self, obj):
        marksheet = obj.get('marksheet_file')
        if marksheet:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(marksheet.url)
            return marksheet.url
        return None


class StudentProfileSerializer(serializers.ModelSerializer):
    semester_marksheets = serializers.SerializerMethodField()
    semester_cgpas = serializers.SerializerMethodField()
    profile_image_url = serializers.SerializerMethodField()
    initial = serializers.SerializerMethodField()
    user = UserSerializer(read_only=True)
    email = serializers.EmailField(source='user.email', read_only=True)
    resume_url = serializers.SerializerMethodField()
    tenth_certificate_url = serializers.SerializerMethodField()
    twelfth_certificate_url = serializers.SerializerMethodField()

    class Meta:
        model = StudentProfile
        fields = '__all__'  # joining_year and passout_year will be included automatically

    def get_profile_image_url(self, obj):
        if obj.profile_image:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.profile_image.url)
            return obj.profile_image.url
        return None

    def get_initial(self, obj):
        return obj.get_initial
        
    def get_resume_url(self, obj):
        if obj.resume:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.resume.url)
            return obj.resume.url
        return None
        
    def get_tenth_certificate_url(self, obj):
        if obj.tenth_certificate:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.tenth_certificate.url)
            return obj.tenth_certificate.url
        return None
        
    def get_twelfth_certificate_url(self, obj):
        if obj.twelfth_certificate:
            request = self.context.get('request')
            if request:
                return request.build_absolute_uri(obj.twelfth_certificate.url)
            return obj.twelfth_certificate.url
        return None

    def get_semester_marksheets(self, obj):
        semesters_data = obj.get_all_semesters_data()
        return SemesterMarksheetSerializer(semesters_data, many=True, context=self.context).data

    def get_semester_cgpas(self, obj):
        """Return semester CGPAs as an array for frontend compatibility"""
        semester_cgpas = []
        for i in range(1, 9):
            cgpa = getattr(obj, f'semester{i}_cgpa', None)
            if cgpa:
                semester_cgpas.append({
                    'semester': i,
                    'cgpa': cgpa
                })
        return semester_cgpas

    def update(self, instance, validated_data):
        """Custom update method to handle semester_cgpas array and individual semester fields"""
        import logging
        logger = logging.getLogger(__name__)

        request = self.context.get('request')

        # Debug logging
        logger.info(f"Update called with validated_data keys: {list(validated_data.keys())}")
        if request and hasattr(request, 'data'):
            logger.info(f"Request data keys: {list(request.data.keys())}")

        # Handle semester_cgpas if present in the request data
        if request and hasattr(request, 'data'):
            semester_cgpas = request.data.get('semester_cgpas')
            if semester_cgpas and isinstance(semester_cgpas, list):
                # Update individual semester CGPA fields from array
                for semester_data in semester_cgpas:
                    if isinstance(semester_data, dict) and 'semester' in semester_data and 'cgpa' in semester_data:
                        semester = semester_data['semester']
                        cgpa = semester_data['cgpa']
                        if 1 <= semester <= 8:
                            setattr(instance, f'semester{semester}_cgpa', cgpa)

            # Also handle individual semester fields sent directly (e.g., semester1_cgpa, semester2_cgpa)
            for i in range(1, 9):
                field_name = f'semester{i}_cgpa'
                if field_name in request.data:
                    setattr(instance, field_name, request.data[field_name])

        # Update all other fields normally
        for attr, value in validated_data.items():
            if attr != 'semester_cgpas':  # Skip this as we handled it above
                setattr(instance, attr, value)

        instance.save()
        return instance


class StudentProfileListSerializer(serializers.ModelSerializer):
    user = UserSerializer(read_only=True)
    semester_marksheets = serializers.SerializerMethodField()

    class Meta:
        model = StudentProfile
        fields = '__all__'  # joining_year and passout_year will be included automatically

    def get_semester_marksheets(self, obj):
        semesters_data = obj.get_all_semesters_data()
        return SemesterMarksheetSerializer(semesters_data, many=True, context=self.context).data






class CompanyJobListingSerializer(serializers.Serializer):
    title = serializers.CharField()
    type = serializers.CharField()
    ctc = serializers.DecimalField(max_digits=12, decimal_places=2)
    stipend = serializers.DecimalField(max_digits=12, decimal_places=2)
    deadline = serializers.DateField(format="%Y-%m-%d")


# Note: CompaniesJSONSerializer removed - we'll use Company model directly now
