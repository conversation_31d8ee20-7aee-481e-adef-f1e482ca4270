from rest_framework import generics, status, permissions, viewsets
from rest_framework.response import Response
from rest_framework.views import APIView
from rest_framework_simplejwt.tokens import RefreshToken
from django.contrib.auth import authenticate
from django.shortcuts import get_object_or_404
from django.http import Http404
from django.core.exceptions import PermissionDenied
from .models import User, StudentProfile, College
from rest_framework.permissions import AllowAny, IsAuthenticated
from .serializers import UserSerializer, StudentProfileSerializer, StudentProfileListSerializer, SemesterMarksheetSerializer
from rest_framework.parsers import <PERSON>PartParser, FormParser
from django.utils import timezone

# EmployerProfile removed
# EmployerProfileSerializer removed
from rest_framework.pagination import PageNumberPagination
import pandas as pd
from rest_framework import filters, status
from django.db import models
from django.shortcuts import get_object_or_404
from college.models import College
# EmployerCompanyDataSerializer removed
# CompaniesJSONSerializer removed

# Import StandardResultsSetPagination for pagination
from jobs.utils import StandardResultsSetPagination, get_correct_pagination_data





class StudentRegistrationView(APIView):
    permission_classes = [AllowAny]

    def post(self, request):
        email = request.data.get('email')
        password = request.data.get('password')
        first_name = request.data.get('first_name')
        last_name = request.data.get('last_name')
        student_id = request.data.get('student_id')
        contact_email = request.data.get('contact_email')
        branch = request.data.get('branch')
        gpa = request.data.get('gpa')
        college_id = request.data.get('college', 1)

        # Check if email already exists before creating user
        if User.objects.filter(email=email).exists():
            return Response(
                {"error": "Email already registered."},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            college = College.objects.get(id=college_id)
        except College.DoesNotExist:
            return Response({"detail": "College not found."}, status=status.HTTP_404_NOT_FOUND)

        user = User.objects.create_user(
            email=email,
            password=password,
            college=college,
            user_type=User.UserType.STUDENT,
        )

        StudentProfile.objects.create(
            user=user,
            college=college,
            first_name=first_name,
            last_name=last_name,
            student_id=student_id,
            contact_email=contact_email,
            branch=branch,
            gpa=gpa,
        )

        return Response({"message": "Student registered successfully."}, status=status.HTTP_201_CREATED)


class LoginView(APIView):
    permission_classes = [permissions.AllowAny]

    def post(self, request, *args, **kwargs):
        email = request.data.get("email")
        password = request.data.get("password")
        user = authenticate(email=email, password=password)
        if not user:
            return Response({"detail": "Invalid credentials."}, status=401)
        refresh = RefreshToken.for_user(user)
        return Response({
            "refresh": str(refresh),
            "access": str(refresh.access_token),
            "user": UserSerializer(user).data
        })




class UserProfileView(APIView):
    permission_classes = [permissions.IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def get(self, request):
        user = request.user
        if user.user_type == User.UserType.STUDENT:
            profile = get_object_or_404(StudentProfile, user=user)
            serializer = StudentProfileSerializer(profile, context={'request': request})
        else:
            serializer = UserSerializer(user)
        return Response(serializer.data)

    def patch(self, request):
        user = request.user
        if user.user_type != User.UserType.STUDENT:
            return Response({"detail": "Only students can upload resumes."}, status=403)

        profile = get_object_or_404(StudentProfile, user=user)

        resume_file = request.FILES.get('resume')
        if not resume_file:
            return Response({"detail": "No resume file provided."}, status=400)

        profile.resume = resume_file
        profile.save()
        return Response({"message": "Resume uploaded successfully."})


# EmployerProfileView removed - no longer needed

class BulkStudentUpdateView(APIView):
    permission_classes = [permissions.IsAdminUser]  # Only admin can access this

    def post(self, request):
        excel_file = request.FILES.get('file')
        if not excel_file:
            return Response({"error": "No file uploaded."}, status=400)

        try:
            df = pd.read_excel(excel_file)

            if "student_id" not in df.columns:
                return Response({"error": "Missing required column: student_id"}, status=400)

            updated = 0
            not_found = []
            duplicates = []

            for _, row in df.iterrows():
                sid = str(row["student_id"]).strip()
                matches = StudentProfile.objects.filter(student_id=sid)

                if matches.count() == 1:
                    student = matches.first()
                    for field in row.index:
                        if field != "student_id" and hasattr(student, field):
                            setattr(student, field, row[field])
                    student.save()
                    updated += 1
                elif matches.count() > 1:
                    duplicates.append(sid)
                else:
                    not_found.append(sid)

            return Response({
                "updated": updated,
                "not_found": not_found,
                "duplicates": duplicates,
            })

        except Exception as e:
            return Response({"error": str(e)}, status=500)





class StudentListView(generics.ListAPIView):
    queryset = StudentProfile.objects.select_related('user').all()
    serializer_class = StudentProfileListSerializer
    permission_classes = [permissions.IsAdminUser]
    pagination_class = StandardResultsSetPagination

    def get_queryset(self):
        queryset = super().get_queryset()
        search_by = self.request.query_params.get('search_by')  # 'name' or 'student_id'
        query = self.request.query_params.get('query')

        if search_by == 'name' and query:
            queryset = queryset.filter(first_name__icontains=query) | queryset.filter(last_name__icontains=query)
        elif search_by == 'student_id' and query:
            queryset = queryset.filter(student_id__icontains=query)

        return queryset

    def list(self, request, *args, **kwargs):
        queryset = self.filter_queryset(self.get_queryset())
        page = self.paginate_queryset(queryset)
        
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            # Use corrected pagination calculation
            pagination_data = get_correct_pagination_data(
                request, 
                self.paginator.page.paginator, 
                self.paginator.page, 
                self.pagination_class.page_size
            )
            return Response({
                'data': serializer.data,
                'pagination': pagination_data
            })
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({'data': serializer.data})



class StudentDetailView(generics.RetrieveAPIView):
    queryset = StudentProfile.objects.select_related('user').all()
    serializer_class = StudentProfileSerializer
    permission_classes = [permissions.IsAdminUser]
    lookup_field = 'id'

class StudentUpdateView(generics.UpdateAPIView):
    queryset = StudentProfile.objects.select_related('user').all()
    serializer_class = StudentProfileSerializer
    permission_classes = [permissions.IsAuthenticated]
    lookup_field = 'id'

    def get_object(self):
        """Allow users to update their own profile or admins to update any profile"""
        user = self.request.user
        profile_id = self.kwargs.get('id')

        if user.user_type == 'ADMIN' or user.is_staff:
            # Admins can update any profile
            return get_object_or_404(StudentProfile, id=profile_id)
        else:
            # Regular users can only update their own profile
            if hasattr(user, 'student_profile') and str(user.student_profile.id) == str(profile_id):
                return user.student_profile
            else:
                raise PermissionDenied("You can only update your own profile")

    def patch(self, request, *args, **kwargs):
        """Handle PATCH requests with debugging"""
        import logging
        logger = logging.getLogger(__name__)

        logger.info(f"StudentUpdateView PATCH request data: {request.data}")

        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=True)

        if not serializer.is_valid():
            logger.error(f"StudentUpdateView validation errors: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        serializer.save()
        return Response(serializer.data)


# EmployerCompanyListView removed - use Company model directly

from jobs.utils import StandardResultsSetPagination

# CompanyListCreateView removed - replaced with Company model direct access


# CompanyDetailUpdateDeleteView removed - use Company model directly


# EmployerRegistrationView removed - no longer needed


from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from rest_framework.authtoken.models import Token

# Add this class if you need logout functionality
class LogoutView(APIView):
    """
    View for user logout - invalidates the token
    """
    def post(self, request):
        # Get the user's token and delete it
        try:
            token = Token.objects.get(user=request.user)
            token.delete()
            return Response({"message": "Successfully logged out."}, status=status.HTTP_200_OK)
        except Token.DoesNotExist:
            return Response({"message": "User not logged in."}, status=status.HTTP_400_BAD_REQUEST)

from rest_framework import viewsets, status
from rest_framework.decorators import api_view, permission_classes, action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.shortcuts import get_object_or_404
from .models import StudentProfile
from .serializers import StudentProfileSerializer, SemesterMarksheetSerializer

class StudentProfileViewSet(viewsets.ModelViewSet):
    serializer_class = StudentProfileSerializer
    permission_classes = [IsAuthenticated]
    lookup_value_regex = '[^/]+'  # Allow 'me' and numeric IDs

    def get_queryset(self):
        # If admin, can see all profiles. Otherwise, only own profile
        user = self.request.user
        if user.user_type == 'ADMIN' or user.is_staff:
            return StudentProfile.objects.all()
        return StudentProfile.objects.filter(user=user)

    def get_object(self):
        # Get profile of logged in user or specified user if admin
        user = self.request.user
        lookup_url_kwarg = self.lookup_url_kwarg or self.lookup_field
        pk = self.kwargs.get(lookup_url_kwarg)

        # Handle 'me' as a special case to get the current user's profile
        if pk == 'me':
            if hasattr(user, 'student_profile'):
                return user.student_profile
            else:
                raise Http404("You don't have a student profile")

        # Handle normal pk lookup
        elif pk:
            if user.user_type == 'ADMIN' or user.is_staff:
                return get_object_or_404(StudentProfile, pk=pk)
            # Non-admins can only view their own profile
            if hasattr(user, 'student_profile') and str(user.student_profile.id) != pk:
                raise PermissionDenied("You cannot access this profile")
            return user.student_profile

        # If no pk specified, return current user's profile
        else:
            if hasattr(user, 'student_profile'):
                return user.student_profile
            else:
                raise Http404("You don't have a student profile")

    def update(self, request, *args, **kwargs):
        """Handle PUT requests"""
        import logging
        logger = logging.getLogger(__name__)

        partial = kwargs.pop('partial', False)
        instance = self.get_object()

        # Debug logging
        logger.info(f"Update request data: {request.data}")
        logger.info(f"Partial update: {partial}")

        serializer = self.get_serializer(instance, data=request.data, partial=partial)

        if not serializer.is_valid():
            logger.error(f"Serializer validation errors: {serializer.errors}")
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        self.perform_update(serializer)
        return Response(serializer.data)

    def partial_update(self, request, *args, **kwargs):
        """Handle PATCH requests"""
        kwargs['partial'] = True
        return self.update(request, *args, **kwargs)
    
    @action(detail=True, methods=['post'])
    def upload_profile_image(self, request, pk=None):
        profile = self.get_object()
        if 'image' not in request.FILES:
            return Response({'error': 'No image provided'}, status=status.HTTP_400_BAD_REQUEST)
            
        profile.profile_image = request.FILES['image']
        profile.save()
        
        return Response({'message': 'Profile image uploaded successfully'})
    
    @action(detail=True, methods=['post'])
    def upload_resume(self, request, pk=None):
        profile = self.get_object()
        if 'resume' not in request.FILES:
            return Response({'error': 'No resume provided'}, status=status.HTTP_400_BAD_REQUEST)
            
        profile.resume = request.FILES['resume']
        profile.save()
        
        return Response({'message': 'Resume uploaded successfully'})
    
    @action(detail=True, methods=['post'])
    def upload_certificate(self, request, pk=None):
        user = self.request.user
        if not user.is_authenticated:
            return Response({'error': 'Authentication required'}, status=status.HTTP_401_UNAUTHORIZED)

        try:
            # First, check if user has a profile directly
            try:
                profile = StudentProfile.objects.get(user=user)
            except StudentProfile.DoesNotExist:
                # Create a new profile if one doesn't exist
                college = getattr(user, 'college', None)
                if not college:
                    try:
                        college = College.objects.get(id=1)
                    except College.DoesNotExist:
                        college = College.objects.create(
                            name='Default College', 
                            slug='default-college'
                        )
                
                # Ensure user is properly assigned with explicit check
                if not user or not user.id:
                    return Response({
                        'error': 'User authentication issue. Please log in again.'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                profile = StudentProfile.objects.create(
                    user=user,
                    college=college,
                    first_name=user.first_name or '..',
                    last_name=user.last_name or '..',
                    contact_email=user.email
                )

            # Now we have a valid profile, proceed with certificate upload
            cert_type = request.data.get('type')
            
            if 'file' not in request.FILES:
                return Response({'error': 'No file provided'}, status=status.HTTP_400_BAD_REQUEST)
                
            if cert_type == '10th':
                profile.tenth_certificate = request.FILES['file']
            elif cert_type == '12th':
                profile.twelfth_certificate = request.FILES['file']
            else:
                return Response({'error': 'Invalid certificate type'}, status=status.HTTP_400_BAD_REQUEST)
            
            profile.save()
            return Response({'message': f'{cert_type} certificate uploaded successfully'})
            
        except Exception as e:
            import traceback
            print(f"Error uploading certificate: {e}")
            print(traceback.format_exc())
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    @action(detail=True, methods=['post'])
    def upload_semester_marksheet(self, request, pk=None):
        profile = self.get_object()
        semester = request.data.get('semester')
        cgpa = request.data.get('cgpa')
        
        if not semester or not semester.isdigit() or not (1 <= int(semester) <= 8):
            return Response({'error': 'Valid semester number (1-8) is required'}, status=status.HTTP_400_BAD_REQUEST)
            
        if not cgpa:
            return Response({'error': 'CGPA is required'}, status=status.HTTP_400_BAD_REQUEST)
            
        if 'marksheet_file' not in request.FILES:
            return Response({'error': 'No marksheet file provided'}, status=status.HTTP_400_BAD_REQUEST)
            
        semester_num = int(semester)
        
        # Set semester CGPA
        setattr(profile, f'semester{semester_num}_cgpa', cgpa)
        
        # Set semester marksheet file
        setattr(profile, f'semester{semester_num}_marksheet', request.FILES['marksheet_file'])
        
        # Set upload date
        setattr(profile, f'semester{semester_num}_upload_date', timezone.now())
        
        profile.save()
        
        return Response({
            'message': f'Semester {semester} marksheet uploaded successfully',
            'semester': semester_num,
            'cgpa': cgpa
        })
    
    @action(detail=True, methods=['get'])
    def semester_marksheets(self, request, pk=None):
        profile = self.get_object()
        semesters_data = profile.get_all_semesters_data()
        serializer = SemesterMarksheetSerializer(semesters_data, many=True, context={'request': request})
        return Response(serializer.data)

    
    @action(detail=True, methods=['get'])
    def semester_marksheets(self, request, pk=None):
        profile = self.get_object()
        semesters_data = profile.get_all_semesters_data()
        serializer = SemesterMarksheetSerializer(semesters_data, many=True, context={'request': request})
        return Response(serializer.data)

